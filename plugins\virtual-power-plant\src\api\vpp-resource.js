// 虚拟电厂资源管理API接口
import fetch from "eem-base/utils/fetch";

const prefix = "/api/v1";

// 创建虚拟电厂
export function createVpp(data) {
  return fetch({
    url: `${prefix}/vpp`,
    method: "POST",
    data
  });
}
// 查询虚拟电厂详情
export function getVpp(id) {
  return fetch({
    url: `${prefix}/vpp/${id}`,
    method: "GET"
  });
}
// 更新虚拟电厂
export function updateVpp(id, data) {
  return fetch({
    url: `${prefix}/vpp/${id}`,
    method: "PUT",
    data
  });
}
// 删除虚拟电厂
export function deleteVpp(id) {
  return fetch({
    url: `${prefix}/vpp/${id}`,
    method: "DELETE"
  });
}
// 查询虚拟电厂列表
export function listVpp(params) {
  return fetch({
    url: `${prefix}/vpp`,
    method: "GET",
    params
  });
}
// 查询虚拟电厂类型
export function getVppTypes(province) {
  return fetch({
    url: `${prefix}/vpp/types/${province}`,
    method: "GET"
  });
}

// 创建用户
export function createUser(data) {
  return fetch({
    url: `${prefix}/user`,
    method: "POST",
    data
  });
}
// 查询用户详情
export function getUser(id) {
  return fetch({
    url: `${prefix}/user/${id}`,
    method: "GET"
  });
}
// 更新用户
export function updateUser(id, data) {
  return fetch({
    url: `${prefix}/user/${id}`,
    method: "PUT",
    data
  });
}
// 删除用户
export function deleteUser(id) {
  return fetch({
    url: `${prefix}/user/${id}`,
    method: "DELETE"
  });
}
// 查询用户列表
export function listUser(params) {
  return fetch({
    url: `${prefix}/user`,
    method: "GET",
    params
  });
}
// 查询虚拟电厂下用户列表
export function listVppUsers(vppId) {
  return fetch({
    url: `${prefix}/vpp/${vppId}/users`,
    method: "GET"
  });
}

// 创建资源
export function createResource(data) {
  return fetch({
    url: `${prefix}/resource`,
    method: "POST",
    data
  });
}
// 查询资源详情
export function getResource(id) {
  return fetch({
    url: `${prefix}/resource/${id}`,
    method: "GET"
  });
}
// 更新资源
export function updateResource(id, data) {
  return fetch({
    url: `${prefix}/resource/${id}`,
    method: "PUT",
    data
  });
}
// 删除资源
export function deleteResource(id) {
  return fetch({
    url: `${prefix}/resource/${id}`,
    method: "DELETE"
  });
}
// 查询资源列表
export function listResource(params) {
  return fetch({
    url: `${prefix}/resource`,
    method: "GET",
    params
  });
}
// 查询用户下资源列表
export function listUserResources(userId) {
  return fetch({
    url: `${prefix}/user/${userId}/resources`,
    method: "GET"
  });
}

// 创建站点
export function createSite(data) {
  return fetch({
    url: `${prefix}/site`,
    method: "POST",
    data
  });
}
// 查询站点详情
export function getSite(id) {
  return fetch({
    url: `${prefix}/site/${id}`,
    method: "GET"
  });
}
// 更新站点
export function updateSite(id, data) {
  return fetch({
    url: `${prefix}/site/${id}`,
    method: "PUT",
    data
  });
}
// 删除站点
export function deleteSite(id) {
  return fetch({
    url: `${prefix}/site/${id}`,
    method: "DELETE"
  });
}
// 查询站点列表
export function listSite(params) {
  return fetch({
    url: `${prefix}/site`,
    method: "GET",
    params
  });
}
// 查询资源下站点列表
export function listResourceSites(resourceId) {
  return fetch({
    url: `${prefix}/resource/${resourceId}/sites`,
    method: "GET"
  });
}

// 创建设备
export function createDevice(data) {
  return fetch({
    url: `${prefix}/device`,
    method: "POST",
    data
  });
}
// 查询设备详情
export function getDevice(id) {
  return fetch({
    url: `${prefix}/device/${id}`,
    method: "GET"
  });
}
// 更新设备
export function updateDevice(id, data) {
  return fetch({
    url: `${prefix}/device/${id}`,
    method: "PUT",
    data
  });
}
// 删除设备
export function deleteDevice(id) {
  return fetch({
    url: `${prefix}/device/${id}`,
    method: "DELETE"
  });
}
// 查询设备列表
export function listDevice(params) {
  return fetch({
    url: `${prefix}/device`,
    method: "GET",
    params
  });
}
// 查询站点下设备列表
export function listSiteDevices(siteId) {
  return fetch({
    url: `${prefix}/site/${siteId}/devices`,
    method: "GET"
  });
}

// 关联数据点
export function associateDatapoints(deviceId, data) {
  return fetch({
    url: `${prefix}/device/${deviceId}/datapoints`,
    method: "POST",
    data
  });
}
// 查询设备下数据点
export function getDeviceDatapoints(deviceId) {
  return fetch({
    url: `${prefix}/device/${deviceId}/datapoints`,
    method: "GET"
  });
}
// 更新数据点
export function updateDatapoint(id, data) {
  return fetch({
    url: `${prefix}/datapoint/${id}`,
    method: "PUT",
    data
  });
}
// 删除数据点
export function deleteDatapoint(id) {
  return fetch({
    url: `${prefix}/datapoint/${id}`,
    method: "DELETE"
  });
}

// ==================== 资源聚合模块 API ====================

// 创建新机组
export function createUnit(data) {
  return fetch({
    url: `${prefix}/resource/agg/units`,
    method: "POST",
    data
  });
}

// 删除机组
export function deleteUnit(unitId) {
  return fetch({
    url: `${prefix}/resource/agg/units/${unitId}`,
    method: "DELETE"
  });
}

// 更新机组信息
export function updateUnit(unitId, data) {
  return fetch({
    url: `${prefix}/resource/agg/units/${unitId}`,
    method: "POST",
    data
  });
}

// 获取机组详情
export function getUnit(unitId) {
  return fetch({
    url: `${prefix}/resource/agg/units/${unitId}`,
    method: "GET"
  });
}

// 获取机组列表
export function listUnits(params) {
  return fetch({
    url: `${prefix}/resource/agg/units`,
    method: "GET",
    params
  });
}

// 获取可绑定的资源列表
export function getAvailableResources(params) {
  return fetch({
    url: `${prefix}/resource/agg/available-resources`,
    method: "GET",
    params
  });
}

// 从机组解绑单个资源
export function unbindResource(unitId, resourceId) {
  return fetch({
    url: `${prefix}/resource/agg/units/${unitId}/resources/${resourceId}`,
    method: "DELETE"
  });
}

// 获取资源区域枚举值
export function getDistricts() {
  return fetch({
    url: `${prefix}/resource/agg/districts`,
    method: "GET"
  });
}
